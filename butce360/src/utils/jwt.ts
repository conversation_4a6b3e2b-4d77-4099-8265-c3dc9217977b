// JWT utility functions for token decoding

export interface JwtClaim {
  ID: string;
  UserName: string;
  UserId: string;
  IsGuest: boolean;
  GuestID: string;
  Plan: string;
  LoginType: number; // 1-email, 2-google, 3-apple
  exp: number;
  iat: number;
}

/**
 * Decode JWT token without verification (client-side only)
 * This is safe for reading non-sensitive claims like LoginType
 */
export const decodeJwtToken = (token: string): JwtClaim | null => {
  try {
    // JWT has 3 parts separated by dots: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('[JWT] Invalid token format');
      return null;
    }

    // Decode the payload (second part)
    const payload = parts[1];
    
    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    
    // Decode base64
    const decodedPayload = atob(paddedPayload);
    
    // Parse JSON
    const claims = JSON.parse(decodedPayload) as JwtClaim;
    
    return claims;
  } catch (error) {
    console.error('[JWT] Error decoding token:', error);
    return null;
  }
};

/**
 * Get LoginType from JWT token
 * @param token JWT token string
 * @returns LoginType number (1-email, 2-google, 3-apple) or null if error
 */
export const getLoginTypeFromToken = (token: string): number | null => {
  const claims = decodeJwtToken(token);
  return claims?.LoginType || null;
};

/**
 * Check if token is expired
 * @param token JWT token string
 * @returns true if expired, false if valid
 */
export const isTokenExpired = (token: string): boolean => {
  const claims = decodeJwtToken(token);
  if (!claims || !claims.exp) {
    return true;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  return claims.exp < currentTime;
};

/**
 * Get user ID from JWT token
 * @param token JWT token string
 * @returns User ID string or null if error
 */
export const getUserIdFromToken = (token: string): string | null => {
  const claims = decodeJwtToken(token);
  return claims?.UserId || null;
};

/**
 * Get login type description
 * @param loginType LoginType number
 * @returns Human readable description
 */
export const getLoginTypeDescription = (loginType: number): string => {
  switch (loginType) {
    case 1:
      return 'Email/Username';
    case 2:
      return 'Google';
    case 3:
      return 'Apple';
    default:
      return 'Unknown';
  }
};
