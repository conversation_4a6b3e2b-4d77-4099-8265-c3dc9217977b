import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Linking,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Logo from '../../components/common/Logo';

const AboutScreen: React.FC = () => {
  const colors = useThemedColors();
  const themedColors = useThemedColors();
  const styles = createStyles(themedColors);

  const handleOpenLink = (url: string) => {
    Linking.openURL(url).catch(err => console.error('Error opening link:', err));
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* App Info */}
        <View style={styles.appInfo}>
          <Logo size={100} variant="rounded" style={styles.appLogo} />
          <Text style={styles.appName}>Butce360</Text>
          <Text style={styles.appVersion}>Versiyon 1.0.0</Text>
          <Text style={styles.appDescription}>
            Kişisel finans yönetiminizi kolaylaştıran modern ve kullanıcı dostu uygulama.
            Gelir ve giderlerinizi takip edin, bütçe oluşturun ve finansal hedeflerinize ulaşın.
          </Text>
        </View>

        {/* Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Özellikler</Text>
          <View style={styles.featureList}>
            <View style={styles.featureItem}>
              <View style={styles.featureIconContainer}>
                <Ionicons name="cash-outline" size={24} color="#6b7280" />
              </View>
              <Text style={styles.featureText}>Gelir ve gider takibi</Text>
            </View>
            <View style={styles.featureItem}>
              <View style={styles.featureIconContainer}>
                <Ionicons name="bar-chart-outline" size={24} color="#6b7280" />
              </View>
              <Text style={styles.featureText}>Detaylı raporlar ve analizler</Text>
            </View>
            <View style={styles.featureItem}>
              <View style={styles.featureIconContainer}>
                <Ionicons name="target-outline" size={24} color="#6b7280" />
              </View>
              <Text style={styles.featureText}>Bütçe planlama ve hedefler</Text>
            </View>
            <View style={styles.featureItem}>
              <View style={styles.featureIconContainer}>
                <Ionicons name="business-outline" size={24} color="#6b7280" />
              </View>
              <Text style={styles.featureText}>Çoklu hesap yönetimi</Text>
            </View>
            <View style={styles.featureItem}>
              <View style={styles.featureIconContainer}>
                <Ionicons name="phone-portrait-outline" size={24} color="#6b7280" />
              </View>
              <Text style={styles.featureText}>Modern ve kullanıcı dostu arayüz</Text>
            </View>
            <View style={styles.featureItem}>
              <View style={styles.featureIconContainer}>
                <Ionicons name="shield-checkmark-outline" size={24} color="#6b7280" />
              </View>
              <Text style={styles.featureText}>Güvenli veri saklama</Text>
            </View>
          </View>
        </View>



        {/* Contact */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>İletişim</Text>
          <TouchableOpacity
            style={styles.contactItem}
            onPress={() => handleOpenLink('mailto:<EMAIL>')}
          >
            <View style={styles.contactIconContainer}>
              <Ionicons name="mail-outline" size={24} color="#6b7280" />
            </View>
            <Text style={styles.contactText}><EMAIL></Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.contactItem}
            onPress={() => handleOpenLink('https://www.butce360.com')}
          >
            <View style={styles.contactIconContainer}>
              <Ionicons name="globe-outline" size={24} color="#6b7280" />
            </View>
            <Text style={styles.contactText}>www.butce360.com</Text>
          </TouchableOpacity>
        </View>

        {/* Legal */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Yasal</Text>
          <TouchableOpacity
            style={styles.legalItem}
            onPress={() => handleOpenLink('https://app.butce360.com/privacy-policy.html')}
          >
            <Text style={styles.legalText}>Gizlilik Politikası</Text>
            <Text style={styles.legalArrow}>›</Text>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            © 2024 Butce360. Tüm hakları saklıdır.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  
  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: colors.text.primary,
  },
  title: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  headerRight: {
    width: 40,
  },

  // Content
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing['4xl'],
  },

  // App Info
  appInfo: {
    alignItems: 'center',
    paddingVertical: spacing['4xl'],
    paddingHorizontal: spacing.screenPadding,
  },
  appLogo: {
    marginBottom: spacing.xl,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  appName: {
    ...typography.styles.h2,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  appVersion: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    marginBottom: spacing.xl,
  },
  appDescription: {
    ...typography.styles.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: 300,
  },

  // Sections
  section: {
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing['2xl'],
  },
  sectionTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.lg,
  },

  // Features
  featureList: {
    gap: spacing.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  featureIconContainer: {
    width: 32,
    height: 32,
    marginRight: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  featureText: {
    ...typography.styles.body2,
    color: colors.text.primary,
    flex: 1,
  },

  // Tech Stack
  techStack: {
    gap: spacing.md,
  },
  techItem: {
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  techName: {
    ...typography.styles.body1,
    color: colors.text.primary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  techDescription: {
    ...typography.styles.caption,
    color: colors.text.secondary,
  },

  // Contact
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    marginBottom: spacing.md,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  contactIconContainer: {
    width: 24,
    height: 24,
    marginRight: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contactText: {
    ...typography.styles.body2,
    color: colors.primary[500],
    flex: 1,
  },

  // Legal
  legalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    marginBottom: spacing.md,
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  legalText: {
    ...typography.styles.body2,
    color: colors.text.primary,
  },
  legalArrow: {
    fontSize: 18,
    color: colors.text.secondary,
  },

  // Footer
  footer: {
    alignItems: 'center',
    paddingVertical: spacing['2xl'],
    paddingHorizontal: spacing.screenPadding,
  },
  footerText: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  footerSubtext: {
    ...typography.styles.caption,
    color: colors.text.tertiary,
    textAlign: 'center',
  },
});

export default AboutScreen;
